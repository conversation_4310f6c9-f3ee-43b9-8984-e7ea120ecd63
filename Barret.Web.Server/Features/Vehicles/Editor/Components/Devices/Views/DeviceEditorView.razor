@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Barret.Web.Server.Shared.Components.DeviceEditors.Tabs
@using Barret.Web.Server.Shared.Components.DeviceManagers
@using Radzen.Blazor
@inherits DeviceEditorViewBase

<div class="barret-complex-dialog device-editor-dialog">
    <div class="barret-dialog-content">
        <div class="barret-dialog-header">
            <div class="flex items-center">
                <i class="bi bi-device-ssd text-blue-600 mr-2 text-xl"></i>
                <span class="text-lg font-semibold text-gray-900">@($"{(ViewModel.IsAdding ? "Add" : "Edit")} {ViewModel.Device.DeviceRole}")</span>
            </div>
        </div>

        <div class="barret-dialog-body">
            <div class="device-editor-container">
                @{
                    var tabs = TabService.GetTabsForDevice(ViewModel.Device!);
                }

                @if (tabs.Any())
                {
                    <RadzenTabs @bind-SelectedIndex="@ViewModel.ActiveTabIndex"
                               class="device-editor-tabs">
                        <Tabs>
                            @foreach (var tab in tabs)
                            {
                                <RadzenTabsItem Text="@tab.Name" Icon="@GetIconClassForTab(tab.Name)">
                                    <div class="tab-content-panel p-4">
                                        @RenderTabContent(tab)
                                    </div>
                                </RadzenTabsItem>
                            }
                        </Tabs>
                    </RadzenTabs>
                }
                else
                {
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        No tabs configured for device role: @ViewModel.Device?.DeviceRole
                    </div>
                }
            </div>
        </div>

        <div class="barret-dialog-footer">
            <div class="flex justify-between items-center p-4">
                <div>
                    @if (!ViewModel.IsDeviceValid)
                    {
                        <div class="barret-alert barret-alert-error">
                            <div class="flex items-center">
                                <i class="bi bi-exclamation-triangle text-red-600 mr-2"></i>
                                <span class="text-red-800 text-sm">Please fill in all required fields</span>
                            </div>
                        </div>
                    }
                </div>
                <div class="barret-dialog-btn-group-right">
                    <RadzenButton Text="Cancel"
                                 Icon="cancel"
                                 ButtonStyle="ButtonStyle.Secondary"
                                 Click="@CancelEdit"
                                 class="barret-btn barret-form-btn" />
                    <RadzenButton Text="@(ViewModel.IsSaving ? "Saving..." : "Save")"
                                 Icon="@(ViewModel.IsSaving ? "" : "save")"
                                 ButtonStyle="ButtonStyle.Primary"
                                 Click="@SaveDeviceAsync"
                                 Disabled="@(!ViewModel.IsDeviceValid || ViewModel.IsSaving)"
                                 IsBusy="@ViewModel.IsSaving"
                                 class="barret-btn barret-form-btn" />
                </div>
            </div>
        </div>
    </div>
</div>
