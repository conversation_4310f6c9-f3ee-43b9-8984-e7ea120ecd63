using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels;
using Microsoft.AspNetCore.Components;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Components
{
    /// <summary>
    /// Code-behind for the DeviceManagerView component.
    /// </summary>
    public partial class DeviceManagerView
    {
        private RadzenDataGrid<DeviceDto>? devicesGrid;

        /// <summary>
        /// Gets or sets the devices to manage.
        /// </summary>
        [Parameter]
        public IEnumerable<DeviceDto> Devices { get; set; } = new List<DeviceDto>();

        /// <summary>
        /// Gets or sets the callback for when a device is updated.
        /// </summary>
        [Parameter]
        public EventCallback<DeviceDto> OnDeviceUpdated { get; set; }

        /// <summary>
        /// Initializes the component.
        /// </summary>
        protected override void OnInitialized()
        {
            // Create the ViewModel
            ViewModel = new DeviceManagerViewModel();
            
            // Subscribe to device updates
            ViewModel.DeviceUpdated += OnViewModelDeviceUpdated;
            
            base.OnInitialized();
        }

        /// <summary>
        /// Called when parameters are set.
        /// </summary>
        protected override void OnParametersSet()
        {
            if (Devices != null && ViewModel != null)
            {
                ViewModel.SetDevices(Devices);
            }
            
            base.OnParametersSet();
        }

        /// <summary>
        /// Shows the connection manager for a device.
        /// </summary>
        /// <param name="device">The device to manage connections for.</param>
        private async Task ShowConnectionManager(DeviceDto device)
        {
            try
            {
                // Expand the row to show the connection manager
                if (devicesGrid != null)
                {
                    await devicesGrid.ExpandRow(device);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error showing connection manager for device {DeviceId}", device.Id);
            }
        }

        /// <summary>
        /// Handles device updates from the ViewModel.
        /// </summary>
        /// <param name="device">The updated device.</param>
        private async void OnViewModelDeviceUpdated(DeviceDto device)
        {
            try
            {
                if (OnDeviceUpdated.HasDelegate)
                {
                    await OnDeviceUpdated.InvokeAsync(device);
                }
                
                // Refresh the grid
                if (devicesGrid != null)
                {
                    await devicesGrid.Reload();
                }
                
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling device update from ViewModel");
            }
        }

        /// <summary>
        /// Handles device updates from child components.
        /// </summary>
        /// <param name="device">The updated device.</param>
        private async Task HandleDeviceUpdated(DeviceDto device)
        {
            try
            {
                if (OnDeviceUpdated.HasDelegate)
                {
                    await OnDeviceUpdated.InvokeAsync(device);
                }

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling device update from child component");
            }
        }

        /// <summary>
        /// Disposes resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing && ViewModel != null)
            {
                ViewModel.DeviceUpdated -= OnViewModelDeviceUpdated;
            }
            
            base.Dispose(disposing);
        }
    }
}
