using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Devices.Alarms;
using Barret.Web.Server.Features.Shared;
using ReactiveUI;
using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Reactive.Linq;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels
{
    /// <summary>
    /// ViewModel for managing device alarms.
    /// </summary>
    public class AlarmManagerViewModel : ViewModelBase
    {
        private DeviceDto _device = null!;
        private ObservableCollection<AlarmDto> _alarms = new();

        /// <summary>
        /// Initializes a new instance of the <see cref="AlarmManagerViewModel"/> class.
        /// </summary>
        public AlarmManagerViewModel()
        {
            // Initialize commands
            AddAlarmCommand = ReactiveCommand.Create(AddAlarm);
            RemoveAlarmCommand = ReactiveCommand.Create<AlarmDto>(RemoveAlarm);
        }

        /// <summary>
        /// Gets or sets the device being managed.
        /// </summary>
        [Reactive]
        public DeviceDto Device
        {
            get => _device;
            set
            {
                this.RaiseAndSetIfChanged(ref _device, value);
                UpdateAlarmsFromDevice();
            }
        }

        /// <summary>
        /// Gets the collection of alarms for the device.
        /// </summary>
        [Reactive]
        public ObservableCollection<AlarmDto> Alarms
        {
            get => _alarms;
            private set => this.RaiseAndSetIfChanged(ref _alarms, value);
        }

        /// <summary>
        /// Gets the command to add a new alarm.
        /// </summary>
        public ReactiveCommand<Unit, Unit> AddAlarmCommand { get; }

        /// <summary>
        /// Gets the command to remove an alarm.
        /// </summary>
        public ReactiveCommand<AlarmDto, Unit> RemoveAlarmCommand { get; }

        /// <summary>
        /// Event raised when the device is changed.
        /// </summary>
        public event Action<DeviceDto>? DeviceChanged;

        /// <summary>
        /// Adds a new alarm to the device.
        /// </summary>
        private void AddAlarm()
        {
            var newAlarm = new AlarmDto
            {
                NotificationType = NotificationType.Alarm,
                NotificationGroupId = NotificationGroupId.MiGeneral,
                WarningId = WarningId.MiCommunicationGeneral,
                DomainDriverId = DomainDriverId.MiGeneral,
                Message = "New Alarm",
                EntityId = Device.Id.ToString(),
                Description = "New Alarm"
            };

            Alarms.Add(newAlarm);
            UpdateDeviceFromAlarms();
            DeviceChanged?.Invoke(Device);
        }

        /// <summary>
        /// Removes an alarm from the device.
        /// </summary>
        /// <param name="alarm">The alarm to remove.</param>
        private void RemoveAlarm(AlarmDto alarm)
        {
            if (Alarms.Contains(alarm))
            {
                Alarms.Remove(alarm);
                UpdateDeviceFromAlarms();
                DeviceChanged?.Invoke(Device);
            }
        }

        /// <summary>
        /// Updates the alarms collection from the device.
        /// </summary>
        private void UpdateAlarmsFromDevice()
        {
            if (Device?.Alarms != null)
            {
                Alarms.Clear();
                foreach (var alarm in Device.Alarms)
                {
                    Alarms.Add(alarm);
                }
            }
        }

        /// <summary>
        /// Updates the device alarms from the collection.
        /// </summary>
        private void UpdateDeviceFromAlarms()
        {
            if (Device != null)
            {
                Device.Alarms = new List<AlarmDto>(Alarms);
            }
        }

        /// <summary>
        /// Updates an alarm property.
        /// </summary>
        /// <param name="alarm">The alarm to update.</param>
        /// <param name="propertyName">The property name.</param>
        /// <param name="value">The new value.</param>
        public void UpdateAlarmProperty(AlarmDto alarm, string propertyName, object value)
        {
            switch (propertyName)
            {
                case nameof(AlarmDto.Description):
                    alarm.Description = value?.ToString() ?? string.Empty;
                    break;
                case nameof(AlarmDto.Message):
                    alarm.Message = value?.ToString() ?? string.Empty;
                    break;
                case nameof(AlarmDto.NotificationType):
                    if (value is NotificationType notificationType)
                        alarm.NotificationType = notificationType;
                    break;
                case nameof(AlarmDto.NotificationGroupId):
                    if (value is NotificationGroupId groupId)
                        alarm.NotificationGroupId = groupId;
                    break;
                case nameof(AlarmDto.WarningId):
                    if (value is WarningId warningId)
                        alarm.WarningId = warningId;
                    break;
                case nameof(AlarmDto.DomainDriverId):
                    if (value is DomainDriverId domainDriverId)
                        alarm.DomainDriverId = domainDriverId;
                    break;
            }

            UpdateDeviceFromAlarms();
            DeviceChanged?.Invoke(Device);
        }

        /// <summary>
        /// Gets the badge CSS class for a notification type.
        /// </summary>
        /// <param name="notificationType">The notification type.</param>
        /// <returns>The CSS class.</returns>
        public string GetBadgeClass(NotificationType notificationType)
        {
            return notificationType == NotificationType.Alarm ? "bg-danger" : "bg-warning";
        }

        /// <summary>
        /// Gets the display text for a notification type.
        /// </summary>
        /// <param name="notificationType">The notification type.</param>
        /// <returns>The display text.</returns>
        public string GetNotificationTypeText(NotificationType notificationType)
        {
            return notificationType.ToString();
        }
    }
}
