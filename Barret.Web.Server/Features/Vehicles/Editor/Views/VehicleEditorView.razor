@page "/vehicles/edit/{Id:guid}"
@page "/vehicles/create"
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Sidebar
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Tabs
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Export
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Import.Views
@using Barret.Web.Server.Features.Vehicles.Editor.Data
@using Barret.Web.Server.Features.Vehicles.Editor.ViewModels

@using DevExpress.Blazor
@using Barret.Core.Areas.DeviceGroups
@inherits VehicleEditorViewBase

<div class="min-h-screen bg-white flex flex-col">
    <!-- Header -->
    <header class="border-b border-gray-100 bg-white z-10">
        <div class="max-w-[1400px] mx-auto px-4 sm:px-6 py-4 flex justify-between items-center">
            <div class="flex items-center gap-4">
                <!-- Mobile menu toggle - only visible on small screens -->
                <button class="h-10 w-10 rounded-full block sm:hidden items-center justify-center text-gray-500 hover:bg-gray-100" @onclick="async () => await ToggleSidebar()">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>

                <!-- Back button -->
                <button class="h-10 w-10 rounded-full hidden md:flex items-center justify-center text-gray-500 hover:bg-gray-100" @onclick="NavigateBack">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="19" y1="12" x2="5" y2="12"></line>
                        <polyline points="12 19 5 12 12 5"></polyline>
                    </svg>
                </button>

                <h1 class="text-xl sm:text-2xl font-medium text-gray-900">
                    @(ViewModel.IsNewVessel ? "Create Vehicle" : $"Edit Vehicle: {ViewModel.Vessel?.Name}")
                </h1>
            </div>

            <div class="flex gap-2 sm:gap-3">
                @if (!ViewModel.IsNewVessel)
                {
                    <button class="flex items-center gap-2 h-9 sm:h-10 px-3 sm:px-4 rounded-full border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors"
                            @onclick="ShowImportDialog">
                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="17 8 12 3 7 8"></polyline>
                            <line x1="12" y1="3" x2="12" y2="15"></line>
                        </svg>
                        <span class="hidden sm:inline">Import</span>
                    </button>

                    <button class="flex items-center gap-2 h-9 sm:h-10 px-3 sm:px-4 rounded-full border border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors"
                            @onclick="ShowExportDialog">
                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        <span class="hidden sm:inline">Export</span>
                    </button>
                }

                <button class="flex items-center gap-2 h-9 sm:h-10 px-3 sm:px-4 rounded-full transition-colors @GetSaveButtonClasses()"
                        @onclick="SaveVessel"
                        disabled="@(!ViewModel.IsNewVessel && !ViewModel.IsDirty || ViewModel.IsSaving)"
                        title="@GetSaveButtonTooltip()">
                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17 21 17 13 7 13 7 21"></polyline>
                        <polyline points="7 3 7 8 15 8"></polyline>
                    </svg>
                    @if (ViewModel.IsSaving)
                    {
                        <span class="hidden sm:inline">Saving...</span>
                    }
                    else
                    {
                        <span class="hidden sm:inline">Save Changes</span>
                    }
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="flex-1 flex">
        @if (ViewModel.IsLoading)
        {
            <div class="flex justify-center items-center my-12 w-full">
                <div class="animate-spin h-8 w-8 border-4 border-gray-200 border-t-gray-800 rounded-full"></div>
                <p class="ml-3 text-gray-700">Loading vehicle...</p>
            </div>
        }
        else if (ViewModel.Vessel != null)
        {
            <!-- Sidebar / Vertical Navigation -->
            <aside class="bg-gray-50 border-r border-gray-100 w-64 flex-shrink-0 flex flex-col transition-all duration-300 ease-in-out fixed md:static inset-y-0 left-0 z-20 md:translate-x-0 @(isSidebarOpen ? "" : "-translate-x-full") h-[calc(100vh-65px)] md:h-auto">
                <EditorSidebarView Vessel="@ViewModel.Vessel"
                                  ActiveTab="@ViewModel.ActiveTab"
                                  OnTabSelected="@(async (tab) => await HandleTabSelected(tab))" />
            </aside>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto p-4 sm:p-6 md:ml-0 w-full">
                <div class="space-y-8 max-w-5xl mx-auto">
                    @if (ViewModel.ActiveTab.Id == TabIdentifier.BasicInfo)
                    {
                        <BasicInfoTabView Vessel="@ViewModel.Vessel"
                                         OnChange="@ViewModel.MarkAsDirty" />
                    }
                    else if (ViewModel.ActiveDeviceGroupType != null && ViewModel.Vessel.DeviceGroups.ContainsKey(ViewModel.ActiveDeviceGroupType.Value))
                    {
                        var deviceGroup = ViewModel.Vessel.DeviceGroups[ViewModel.ActiveDeviceGroupType.Value];
                        <DeviceGroupTabView GroupType="@deviceGroup.Type"
                                          DeviceGroup="@deviceGroup"
                                          OnDeviceAdded="@HandleDeviceAdded"
                                          OnDeviceRemoved="@HandleDeviceRemoved"
                                          OnDeviceUpdated="@HandleDeviceUpdated"
                                          VehicleId="@ViewModel.Vessel.Id"
                                          Vessel="@ViewModel.Vessel" />
                    }
                </div>
            </main>
        }
    </div>
</div>

<!-- Leave Confirmation Dialog -->
@if (showLeaveConfirmation)
{
    <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Unsaved Changes</h3>
                <button class="text-gray-400 hover:text-gray-500" @onclick="CancelLeave">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="mb-6">
                <p class="text-gray-600">You have unsaved changes. Are you sure you want to leave?</p>
            </div>
            <div class="flex justify-end gap-3">
                <button class="px-4 py-2 rounded-full border border-gray-300 text-gray-700 hover:bg-gray-50" @onclick="CancelLeave">
                    Cancel
                </button>
                <button class="px-4 py-2 rounded-full bg-gray-900 text-white hover:bg-gray-800" @onclick="ConfirmLeave">
                    Leave
                </button>
            </div>
        </div>
    </div>
}

@* Export and Import Dialogs are now handled via DialogService.OpenAsync in VehicleEditorViewBase *@
