@using Radzen.Blazor
@using Barret.Web.Server.Features.Shared.Enums

<RadzenTextBox Value="@Text"
               ValueChanged="@OnTextChanged"
               Placeholder="@Placeholder"
               ReadOnly="@ReadOnly"
               Disabled="@(!Enabled)"
               ShowClearButton="@(ClearButtonDisplayMode != ClearButtonDisplayMode.Never)"
               class="@($"barret-input {GetSizeClass()} {CssClass}")">
</RadzenTextBox>

@code {
    /// <summary>
    /// Gets or sets the text value.
    /// </summary>
    [Parameter]
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Event callback for when the text changes.
    /// </summary>
    [Parameter]
    public EventCallback<string> TextChanged { get; set; }

    /// <summary>
    /// Gets or sets the placeholder text.
    /// </summary>
    [Parameter]
    public string Placeholder { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the textbox is read-only.
    /// </summary>
    [Parameter]
    public bool ReadOnly { get; set; }

    /// <summary>
    /// Gets or sets whether the textbox is enabled.
    /// </summary>
    [Parameter]
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the CSS class.
    /// </summary>
    [Parameter]
    public string CssClass { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the clear button display mode.
    /// </summary>
    [Parameter]
    public ClearButtonDisplayMode ClearButtonDisplayMode { get; set; } = ClearButtonDisplayMode.Auto;

    /// <summary>
    /// Gets or sets the size mode.
    /// </summary>
    [Parameter]
    public ComponentSizeMode SizeMode { get; set; } = ComponentSizeMode.Medium;

    /// <summary>
    /// Handles the text changed event.
    /// </summary>
    /// <param name="newValue">The new text value.</param>
    private async Task OnTextChanged(string newValue)
    {
        if (Text != newValue)
        {
            Text = newValue;
            if (TextChanged.HasDelegate)
            {
                await TextChanged.InvokeAsync(newValue);
            }
        }
    }

    /// <summary>
    /// Gets the appropriate CSS size class based on SizeMode.
    /// </summary>
    /// <returns>The CSS class for the current size mode.</returns>
    private string GetSizeClass()
    {
        return SizeMode switch
        {
            SizeMode.Small => "barret-form-input-sm",
            SizeMode.Large => "barret-form-input-lg",
            _ => "barret-form-input"
        };
    }
}
