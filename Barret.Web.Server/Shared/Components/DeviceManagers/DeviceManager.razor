@using Barret.Shared.DTOs.Devices.Alarms
<!-- View models have been removed in favor of using DTOs directly -->
@using Barret.Web.Server.Shared.Components.DeviceEditors
@using DevExpress.Blazor
@using Barret.Services.Core.Areas.Devices.Queries
@using Barret.Services.Core.Areas.Manufacturers
@using Barret.Services.Core.Areas.DeviceModels.Queries
@using Barret.Services.Core.Areas.Vehicles
@using Barret.Shared.Factories
@using Barret.Shared.DTOs.Devices

@using Barret.Core.Areas.Devices.Enums
@using Barret.Core.Areas.DeviceGroups
@using Barret.Web.Server.Shared.Components.DeviceManagers
@using Barret.Web.Server.Services
@using Barret.Services.Core.Areas.Devices.Factories
@using Barret.Web.Server.Extensions
@using Barret.Shared.DTOs.Vehicles.Vessels
@using Barret.Shared.Results
@using DevExpress.Blazor.Internal
@using Microsoft.Extensions.Logging
@using System.Text
@using Barret.Services.Areas.Devices.Factories
@using Barret.Core.Areas.Vehicles.Models.Vessel

@inject IDeviceQueryService DeviceQueryService
@inject IManufacturerService ManufacturerService
@inject IDeviceModelQueryService DeviceModelQueryService
@inject IVehicleService<Vessel, VesselDto> VehicleService
@inject IJSRuntime JSRuntime
@inject IBarretToastNotificationService ToastService
@inject ILogger<DeviceManager> Logger
@inject IDialogService DialogService
@inject Radzen.DialogService RadzenDialogService

<div class="container">
    <!-- Header with Add Device button -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>@Category.GetDisplayName() Devices</h3>
        <div class="barret-btn-group">
            <RadzenButton Icon="add" Text="Add Device" Click="@OpenAddDeviceDialog"
                         ButtonStyle="ButtonStyle.Primary"
                         class="barret-btn barret-header-btn" />
        </div>
    </div>

    <!-- Import Devices Popup Component moved to VesselEdit.razor -->

    <!-- Devices List -->
    @try
    {
        @if (Devices == null || !Devices.Any())
        {
        <div class="alert alert-info d-flex align-items-center p-4 mb-4">
            <div class="me-3 fs-3">
                <i class="bi bi-info-circle-fill"></i>
            </div>
            <div>
                <h5 class="alert-heading mb-2">No Devices Found</h5>
                <p class="mb-0">You haven't added any devices to the @Category.GetDisplayName() category yet. Click the "Add Device" button above to create one.</p>
            </div>
        </div>

        <div class="text-center py-5 my-4 border rounded bg-light">
            <div class="mb-3">
                <i class="bi bi-devices fs-1 text-muted"></i>
            </div>
            <h4 class="mb-2">This category is empty</h4>
            <p class="text-muted mb-4">Start by adding a new device to the @Category.GetDisplayName() category</p>
            <RadzenButton Icon="add" Text="Add Your First Device" Click="@OpenAddDeviceDialog"
                         ButtonStyle="ButtonStyle.Primary"
                         class="barret-btn barret-btn-lg" />
        </div>
    }
    else
    {
            <DxGrid @ref="Grid"
                    Data="@Devices"
                    ShowFilterRow="true"
                    ShowPager="true"
                    PageSize="10"
                    KeyboardNavigationEnabled="true"
                    SelectionEnabled="true"
                    ShowLoadingPanel="true"
                    LoadingPanelText="Loading devices..."
                    CssClass="device-grid">
            <Columns>
                <DxGridDataColumn FieldName="Name" Caption="Name" />
                <DxGridDataColumn Caption="IP Address">
                    <CellDisplayTemplate>
                        @((context.DataItem as DeviceDto)?.Connection?.IPAddress ?? "-")
                    </CellDisplayTemplate>
                </DxGridDataColumn>
                <DxGridDataColumn Caption="Manufacturer">
                    <CellDisplayTemplate>
                        @((context.DataItem as DeviceDto)?.ManufacturerName ?? "-")
                    </CellDisplayTemplate>
                </DxGridDataColumn>
                <DxGridDataColumn Caption="Model">
                    <CellDisplayTemplate>
                        @((context.DataItem as DeviceDto)?.ModelName ?? "-")
                    </CellDisplayTemplate>
                </DxGridDataColumn>
                <DxGridDataColumn Caption="Connections" Width="120px">
                    <CellDisplayTemplate>
                        @{
                            var device = (context.DataItem as DeviceDto);
                            var totalConnections = device?.Connections?.Count ?? 0;
                            var rowVisibleIndex = context.VisibleIndex;

                            <div class="d-flex align-items-center">
                                <span class="badge bg-info me-2">@totalConnections</span>
                                <button class="btn btn-sm btn-link p-0" @onclick="@(() => ToggleDetailRow(rowVisibleIndex))">
                                    <i class="bi @(Grid.IsDetailRowExpanded(rowVisibleIndex) ? "bi-chevron-up" : "bi-chevron-down")"></i>
                                </button>
                            </div>
                        }
                    </CellDisplayTemplate>
                </DxGridDataColumn>
                <DxGridDataColumn Caption="Alarms" Width="100px">
                    <CellDisplayTemplate>
                        @{
                            var device = (context.DataItem as DeviceDto);
                            var alarmCount = device?.Alarms?.Count ?? 0;
                            var expandedAlarmsKey = $"alarms_{device?.Id}";
                            bool isAlarmsExpanded = expandedRows.ContainsKey(expandedAlarmsKey) && expandedRows[expandedAlarmsKey];

                            <div class="d-flex align-items-center">
                                <span class="badge bg-warning text-dark me-2">@alarmCount</span>
                                <button class="btn btn-sm btn-link p-0" @onclick="@(() => ToggleRowExpansion(expandedAlarmsKey))">
                                    <i class="bi @(isAlarmsExpanded ? "bi-chevron-up" : "bi-chevron-down")"></i>
                                </button>
                            </div>

                            @if (isAlarmsExpanded && device != null)
                            {
                                <div class="expanded-cell-content mt-2">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="fw-bold">Alarms</span>
                                        <button class="btn btn-sm btn-outline-primary"
                                                @onclick="@(() => OpenAddAlarmDialog(device))">
                                            <i class="bi bi-plus-lg"></i> Add
                                        </button>
                                    </div>

                                    @if (device.Alarms?.Any() == true)
                                    {
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Description</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var alarm in device.Alarms)
                                                {
                                                    <tr>
                                                        <td>@alarm.Description</td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm">
                                                                <button class="btn btn-outline-primary"
                                                                        @onclick="@(() => HandleEditAlarm(device, alarm))">
                                                                    <i class="bi bi-pencil"></i>
                                                                </button>
                                                                <button class="btn btn-outline-danger"
                                                                        @onclick="@(() => HandleDeleteAlarm(device, alarm))">
                                                                    <i class="bi bi-trash"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    }
                                    else
                                    {
                                        <div class="alert alert-light py-2">No alarms configured</div>
                                    }
                                </div>
                            }
                        }
                    </CellDisplayTemplate>
                </DxGridDataColumn>
                <DxGridDataColumn Caption="Actions" Width="120px">
                    <CellDisplayTemplate>
                        @{
                            var device = (context.DataItem as DeviceDto);
                            if (device != null)
                            {
                                <div class="barret-btn-group-sm">
                                    <RadzenButton Icon="edit"
                                                 Variant="Variant.Outlined"
                                                 ButtonStyle="ButtonStyle.Primary"
                                                 Size="ButtonSize.Small"
                                                 Click="@(() => HandleEditDevice(device))"
                                                 title="Edit Device"
                                                 class="barret-btn barret-action-btn" />
                                    <RadzenButton Icon="delete"
                                                 Variant="Variant.Outlined"
                                                 ButtonStyle="ButtonStyle.Danger"
                                                 Size="ButtonSize.Small"
                                                 Click="@(() => HandleDeleteDevice(device))"
                                                 title="Delete Device"
                                                 class="barret-btn barret-action-btn" />
                                </div>
                            }
                        }
                    </CellDisplayTemplate>
                </DxGridDataColumn>
            </Columns>

            <!-- DetailRowTemplate removed - InterfaceManager deprecated -->
            <!-- TODO: Replace with modern connection management in Features directory -->
            </DxGrid>
    }

    <!-- Device Editor - DEPRECATED: This component is deprecated and should be replaced with the new MVVM device editor -->

    <!-- Role Selector Component - Now handled via DialogService -->
    }
    catch (Exception ex)
    {
        <div class="alert alert-danger">
            <h4 class="alert-heading">Error Rendering DeviceManager</h4>
            <p>An error occurred while rendering the device manager component:</p>
            <pre class="mb-0">@ex.Message</pre>
            <pre class="small text-muted">@ex.StackTrace</pre>
        </div>
    }
</div>

@code {
    #region Parameters

    [Parameter]
    public List<DeviceDto> Devices { get; set; } = new List<DeviceDto>();

    [Parameter]
    public DeviceGroups Category { get; set; } = DeviceGroups.CameraGroup;

    [Parameter]
    public List<DeviceRole> AllowedRoles { get; set; } = new List<DeviceRole>();

    [Parameter]
    public EventCallback<DeviceDto> OnDeviceAdded { get; set; }

    [Parameter]
    public EventCallback<DeviceDto> OnDeviceUpdated { get; set; }

    [Parameter]
    public EventCallback<Guid> OnDeviceRemoved { get; set; }

    [Parameter]
    public Guid VehicleId { get; set; }

    #endregion

    private IGrid Grid { get; set; } // Reference to the DevExpress grid
    // private DeviceEditor deviceEditor; // DEPRECATED: Removed old device editor
    private List<ManufacturerInfo> manufacturers = new List<ManufacturerInfo>();
    private Dictionary<string, bool> expandedRows = new(); // Still needed for alarms
    private bool isLoading = false;

    protected override void OnInitialized()
    {
        Logger.LogInformation("DeviceManager initialized for category {Category}", Category);
        Logger.LogInformation("Devices is {NullStatus} for category {Category}", Devices == null ? "null" : "not null", Category);
        Logger.LogInformation("Devices count: {Count} for category {Category}", Devices?.Count ?? 0, Category);
    }

    protected override void OnParametersSet()
    {
        Logger.LogInformation("DeviceManager parameters set for category {Category}", Category);
        Logger.LogInformation("Devices is {NullStatus} for category {Category}", Devices == null ? "null" : "not null", Category);
        Logger.LogInformation("Devices count: {Count} for category {Category}", Devices?.Count ?? 0, Category);

        // Ensure we don't have duplicate devices by ID
        if (Devices != null && Devices.Any())
        {
            // Check for and remove duplicate devices
            var uniqueDeviceIds = new HashSet<Guid>();
            var duplicateDevices = new List<DeviceDto>();

            foreach (var device in Devices.ToList())
            {
                if (device.Id != Guid.Empty)
                {
                    if (!uniqueDeviceIds.Add(device.Id))
                    {
                        // This is a duplicate device
                        Logger.LogWarning("Found duplicate device with ID {DeviceId} in category {Category}",
                            device.Id, Category);
                        duplicateDevices.Add(device);
                    }
                    else
                    {
                        Logger.LogInformation("Device in {Category}: {DeviceName}, Role: {DeviceRole}, ID: {DeviceId}",
                            Category, device.Name, device.DeviceRole, device.Id);
                    }
                }
            }

            // Remove duplicate devices
            if (duplicateDevices.Any())
            {
                foreach (var duplicate in duplicateDevices)
                {
                    Devices.Remove(duplicate);
                    Logger.LogInformation("Removed duplicate device {DeviceName} with ID {DeviceId} from category {Category}",
                        duplicate.Name, duplicate.Id, Category);
                }
            }
        }
    }

    protected override void OnAfterRender(bool firstRender)
    {
        Logger.LogInformation("DeviceManager rendered for category {Category}, firstRender: {FirstRender}", Category, firstRender);
        Logger.LogInformation("After render - Devices count: {Count} for category {Category}", Devices?.Count ?? 0, Category);

        // Log all devices in the category
        if (Devices != null && Devices.Any())
        {
            Logger.LogInformation("After render - Found {Count} devices in category {Category}", Devices.Count, Category);
            foreach (var device in Devices)
            {
                Logger.LogInformation("After render - Device in category {Category}: {DeviceName}, Role: {DeviceRole}, ID: {DeviceId}",
                    Category, device.Name, device.DeviceRole, device.Id);
            }
        }
    }

    // Toggle using DevExpress Grid's built-in detail row functionality
    private void ToggleDetailRow(int visibleIndex)
    {
        if (Grid.IsDetailRowExpanded(visibleIndex))
            Grid.CollapseDetailRow(visibleIndex);
        else
            Grid.ExpandDetailRow(visibleIndex);
    }

    // Keep this method for alarm expansion
    private void ToggleRowExpansion(string key)
    {
        if (!expandedRows.ContainsKey(key))
        {
            expandedRows[key] = true;
        }
        else
        {
            expandedRows[key] = !expandedRows[key];
        }
        StateHasChanged();
    }

    private async Task OpenAddDeviceDialog()
    {
        try
        {
            // Use the allowed roles passed as a parameter
            var allowedRoles = AllowedRoles ?? new List<DeviceRole>();

            // Log the allowed roles for debugging
            Logger.LogInformation("Opening add device dialog for category {Category} with allowed roles: {AllowedRoles}",
                Category, string.Join(", ", allowedRoles));

            // If there are no allowed roles, show a warning and return
            if (!allowedRoles.Any())
            {
                Logger.LogWarning("No allowed roles defined for category {Category}", Category);
                ToastService.ShowToast(new ToastOptions
                {
                    Title = "Warning",
                    Text = $"No device types are defined for {Category}",
                    IconCssClass = "bi bi-exclamation-triangle",
                    CssClass = "toast-warning",
                    ShowIcon = true
                });
                return;
            }

            // Show a dialog to select which device role to add
            var selectedRole = await ShowDeviceRoleSelectionDialog(allowedRoles);

            if (selectedRole != null)
            {
                // Create a new device with the selected role and device group type using the static device DTO factory
                var newDevice = DeviceDTOFactory.CreateDto(selectedRole.Value, Category);

                // Explicitly set the device role to ensure it's correct
                newDevice.DeviceRole = selectedRole.Value;

                // Explicitly set the device group type to ensure it's correct
                newDevice.DeviceGroupType = Category;

                // Log detailed information about the device creation
                Logger.LogInformation("Created new device with role: {Role}, Category: {Category}",
                    newDevice.DeviceRole, Category);

                // Double-check the device role
                if (newDevice.DeviceRole != selectedRole.Value)
                {
                    Logger.LogWarning("Device role mismatch! Expected: {ExpectedRole}, Actual: {ActualRole}",
                        selectedRole.Value, newDevice.DeviceRole);

                    // Force the correct role
                    newDevice.DeviceRole = selectedRole.Value;
                    Logger.LogInformation("Forced device role to: {Role}", newDevice.DeviceRole);
                }

                Logger.LogDebug("Opening add device dialog for role {Role}", newDevice.DeviceRole);

                // Load manufacturers filtered by device role
                Logger.LogInformation("DeviceManager: Loading manufacturers for device role {Role}", newDevice.DeviceRole);
                var result = await ManufacturerService.GetManufacturersByDeviceRoleAsync(newDevice.DeviceRole);
                if (result.Success)
                {
                    manufacturers = result.Data.ToList();
                    Logger.LogInformation("DeviceManager: Loaded {Count} manufacturers for device role {Role}",
                        manufacturers.Count, newDevice.DeviceRole);

                    // Log each manufacturer
                    foreach (var manufacturer in manufacturers)
                    {
                        Logger.LogInformation("DeviceManager: Manufacturer: {ManufacturerName} (ID: {ManufacturerId})",
                            manufacturer.Name, manufacturer.Id);
                    }
                }
                else
                {
                    Logger.LogWarning("DeviceManager: Failed to load manufacturers for role {Role}: {Error}",
                        newDevice.DeviceRole, result.ErrorMessage);

                    // Fallback to loading all manufacturers
                    Logger.LogWarning("DeviceManager: Falling back to loading all manufacturers");
                    var fallbackResult = await ManufacturerService.GetAllManufacturersAsync();
                    if (fallbackResult.Success)
                    {
                        manufacturers = fallbackResult.Data.ToList();
                        Logger.LogWarning("DeviceManager: Loaded {Count} manufacturers (fallback)", manufacturers.Count);

                        // Log each manufacturer in fallback mode
                        foreach (var manufacturer in manufacturers)
                        {
                            Logger.LogInformation("DeviceManager: Fallback - Manufacturer: {ManufacturerName} (ID: {ManufacturerId})",
                                manufacturer.Name, manufacturer.Id);
                        }
                    }
                }

                // Open the device editor
                // await deviceEditor.OpenForAdd(newDevice, manufacturers); // DEPRECATED: Old device editor removed
                Logger.LogWarning("Device editor functionality is deprecated and needs to be replaced with new MVVM editor");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error opening add device dialog: {Message}", ex.Message);
            await JSRuntime.InvokeVoidAsync("alert", $"Error opening add device dialog: {ex.Message}");
        }
    }

    private async Task<DeviceRole?> ShowDeviceRoleSelectionDialog(IEnumerable<DeviceRole> allowedRoles)
    {
        // If there's only one allowed role, return it without showing a dialog
        if (allowedRoles.Count() == 1)
        {
            return allowedRoles.First();
        }

        // Create a temporary device with Generic role
        // We're not using the device role for compatibility, but just as a container
        var tempDevice = new DeviceDto
        {
            Id = Guid.NewGuid(),
            Name = "Temporary Device",
            DeviceRole = DeviceRole.Generic
        };

        // Open the role selector dialog using the dialog service
        var result = await RadzenDialogService.OpenAsync<RoleSelector>(
            "Select Device Role",
            new Dictionary<string, object>
            {
                { "SourceDevice", tempDevice },
                { "CompatibleRoles", allowedRoles.ToList() }
            },
            new Radzen.DialogOptions()
            {
                Width = "500px",
                Height = "auto",
                Resizable = false,
                Draggable = true,
                CloseDialogOnEsc = true,
                CloseDialogOnOverlayClick = false,
                CssClass = "barret-complex-dialog role-selector-dialog"
            });

        // The result will be the selected DeviceRole or null if cancelled
        return result as DeviceRole?;
    }

    // Get compatible roles for a device
    private List<DeviceRole> GetCompatibleRoles(DeviceDto device)
    {
        // Use the allowed roles passed as a parameter
        return AllowedRoles?.ToList() ?? new List<DeviceRole>();
    }

    private async Task HandleEditDevice(DeviceDto device)
    {
        Logger.LogInformation("Opening edit device dialog for role {Role}", device.DeviceRole);

        // Load manufacturers filtered by device role
        Logger.LogInformation("HandleEditDevice: Loading manufacturers for device role {Role}", device.DeviceRole);
        var result = await ManufacturerService.GetManufacturersByDeviceRoleAsync(device.DeviceRole);
        if (result.Success)
        {
            manufacturers = result.Data.ToList();
            Logger.LogInformation("HandleEditDevice: Loaded {Count} manufacturers for device role {Role}",
                manufacturers.Count, device.DeviceRole);

            // Log each manufacturer
            foreach (var manufacturer in manufacturers)
            {
                Logger.LogInformation("HandleEditDevice: Manufacturer: {ManufacturerName} (ID: {ManufacturerId})",
                    manufacturer.Name, manufacturer.Id);
            }
        }
        else
        {
            Logger.LogWarning("HandleEditDevice: Failed to load manufacturers for role {Role}: {Error}",
                device.DeviceRole, result.ErrorMessage);

            // Fallback to loading all manufacturers
            Logger.LogWarning("HandleEditDevice: Falling back to loading all manufacturers");
            var fallbackResult = await ManufacturerService.GetAllManufacturersAsync();
            if (fallbackResult.Success)
            {
                manufacturers = fallbackResult.Data.ToList();
                Logger.LogWarning("HandleEditDevice: Loaded {Count} manufacturers (fallback)", manufacturers.Count);

                // Log each manufacturer in fallback mode
                foreach (var manufacturer in manufacturers)
                {
                    Logger.LogInformation("HandleEditDevice: Fallback - Manufacturer: {ManufacturerName} (ID: {ManufacturerId})",
                        manufacturer.Name, manufacturer.Id);
                }
            }
        }

        // Open the device editor
        // await deviceEditor.OpenForEdit(device, manufacturers); // DEPRECATED: Old device editor removed
        Logger.LogWarning("Device editor functionality is deprecated and needs to be replaced with new MVVM editor");
    }

    private async Task HandleSaveDevice(DeviceDto device)
    {
        try
        {
            // Check if we're adding or updating
            bool isExisting = Devices.Any(d => d.Id == device.Id);

            if (!isExisting)
            {
                // Pass the device to parent for adding
                // The parent (VesselEdit) will add it to the appropriate collection
                // and then refresh our Devices collection
                Logger.LogDebug("Sending new device {DeviceName} with role {DeviceRole} to parent for adding",
                    device.Name, device.DeviceRole);

                // Double-check that the device role is not Generic unless it's supposed to be
                if (device.DeviceRole == DeviceRole.Generic)
                {
                    Logger.LogWarning("Device role is Generic - this may cause issues if the collection doesn't support Generic devices");
                }

                await OnDeviceAdded.InvokeAsync(device);
            }
            else
            {
                // For updates to existing devices
                await OnDeviceUpdated.InvokeAsync(device);

                // No need to update local collection - parent will refresh it
                Logger.LogDebug("Updated device {DeviceName} with role {DeviceRole}",
                    device.Name, device.DeviceRole);
            }

            // Reload device models to ensure the UI is up-to-date
            // This is important when a new model has been added
            // if (deviceEditor != null)
            // {
            //     await deviceEditor.ReloadDeviceModels();
            // }
            // DEPRECATED: Old device editor removed
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving device: {Message}", ex.Message);
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving device: {ex.Message}");
        }
    }

    private async Task HandleDeleteDevice(DeviceDto device)
    {
        var result = await RadzenDialogService.Confirm(
            $"Are you sure you want to remove the device '{device.Name}'?",
            "Confirm Deletion",
            new Radzen.ConfirmOptions()
            {
                OkButtonText = "Delete",
                CancelButtonText = "Cancel",
                Width = "450px",
                CssClass = "barret-confirmation-dialog"
            });

        if (result == true)
        {
            await OnDeviceRemoved.InvokeAsync(device.Id);
        }
    }



    // This method is no longer needed as we now receive AllowedRoles as a parameter
    // Keeping a simplified version for backward compatibility with other methods
    private IEnumerable<DeviceRole> GetAllowedRolesForCollection(DeviceGroups category)
    {
        return AllowedRoles ?? new List<DeviceRole>();
    }

    private void CreateToastAtRuntime() {
        ToastService.ShowToast(new ToastOptions {
            Title = "Notification",
            Text = "The process is completed.",
            ShowIcon = false,
            CssClass = "italic-text",
            ThemeMode = ToastThemeMode.Pastel,
            ShowCloseButton = false,
        });
    }

    private DeviceDto CreateNewDeviceForCategory(DeviceGroups category)
    {
        // Get the allowed roles for this category
        var allowedRoles = AllowedRoles?.ToList() ?? new List<DeviceRole>();

        // Log the allowed roles for debugging
        Logger.LogInformation("Creating new device for category {Category} with allowed roles: {AllowedRoles}",
            category, string.Join(", ", allowedRoles));

        // If there are no allowed roles, use a Generic device
        if (!allowedRoles.Any())
        {
            Logger.LogWarning("No allowed roles defined for category {Category}, using Generic role", category);
            return DeviceDTOFactory.CreateDto(DeviceRole.Generic, category);
        }

        // Use the first allowed role for this category
        var defaultRole = allowedRoles.First();

        // Create a device with the default role
        var newDevice = DeviceDTOFactory.CreateDto(defaultRole, category);

        // Log the device creation details
        Logger.LogInformation("Created new device for category {Category} with role {Role}",
            category, newDevice.DeviceRole);

        return newDevice;
    }

    // Removed FilterManufacturersByDeviceRole - moved to DeviceEditorFactory

    private string GetDeviceTypeName()
    {
        // Get the allowed roles for this collection
        var allowedRoles = GetAllowedRolesForCollection(Category);

        // If there's only one allowed role, return its display name
        if (allowedRoles.Count() == 1)
        {
            return allowedRoles.First().ToString();
        }

        // Otherwise, return a generic name based on the category name
        return Category.GetDisplayName().TrimEnd('s');
    }

    // Alarm handling methods
    private void OpenAddAlarmDialog(DeviceDto device)
    {
        if (device.Alarms == null)
            device.Alarms = new List<AlarmDto>();

        device.Alarms.Add(new AlarmDto
        {
            Id = Guid.NewGuid(),
            Description = "New Alarm"
        });

        // Notify that device was updated
        OnDeviceUpdated.InvokeAsync(device);
    }

    private void HandleEditAlarm(DeviceDto device, AlarmDto alarm)
    {
        // Open modal to edit alarm or implement inline editing
        // For now just update the parent device
        OnDeviceUpdated.InvokeAsync(device);
    }

    private void HandleDeleteAlarm(DeviceDto device, AlarmDto alarm)
    {
        ToastService.ShowToast(new ToastOptions
        {
            Title = "Delete Alarm",
            Text = "Are you sure you want to remove this alarm?",
            ShowCloseButton = true,
            DisplayTime = TimeSpan.FromSeconds(3),
            FreezeOnClick = true,
            Click = async (args) =>
            {
                device.Alarms.Remove(alarm);
                await OnDeviceUpdated.InvokeAsync(device);
            }
        });
    }

    // Import functionality moved to VesselEdit.razor

    // These methods have been removed as they depend on the Vessel property
    // which has been removed from the component parameters.
    // The parent component (DeviceGroupTab) is now responsible for managing
    // the device list and passing it to this component.
}
