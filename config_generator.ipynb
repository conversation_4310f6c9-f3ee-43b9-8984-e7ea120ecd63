import sys
!{sys.executable} -m pip install pandas
!{sys.executable} -m pip install openpyxl


import pandas as pd
import uuid
import json

allowed_propulsion_action_ids = ['ControlAction', 'ControlFeedback', 'ActionDirection', 'FeedbackDirection', 'ActionPower', 'FeedbackPower', 'FeedbackRpm', 'ActionRot', 'RequestedDirection', 'RequestedPower', 'KeepAlivePolling', 'ActionTogglePropulsion', 'PropulsionStatusFeedback', 'ToggleActionPulse', 'LocalControlRequest', 'PilotModeRequest', 'PilotModeFeedback', 'ClutchFeedbackForward', 'ClutchFeedbackNeutral', 'ClutchFeedbackReverse', 'StartPropulsionAction', 'StopPropulsionAction', 'PilotManualModeRequest', 'PilotRotModeRequest', 'PilotManualModeFeedback', 'PilotRotModeFeedback', 'ThirdPartySafetyContactFeedback']
allowed_system_function_action_ids = ["ControlAction", "ControlFeedback", "ToggleAction", "ActionFeedback", "MoveUpAction", "MoveDownAction", "MoveLeftAction", "MoveRightAction", "MoveUpFeedback", "MoveDownFeedback", "MoveLeftFeedback", "MoveRightFeedback", "ToggleActionPulse", "KeepAlivePolling", "RegisterFillValue", 'ThirdPartySafetyContactFeedback']
allowed_alarm_ids = ["MiBatteryLowVoltage", "MiBatteryHighVoltage", "MiTemperatureExceededThresholds", "MiPropulsionNotReady", "MiOilPressureOutOfBounds", "MiCoolantLiquidOutOfBounds", "MiPropulsionOverspeed", "MiFuelOutOfBounds", "MiGeneratorGeneral", "MiBilgeGeneral", "MiSpudpoleGeneral", "MiPowerGeneral", "MiFireGeneral", "MiCommunicationGeneral", "MiNavigationEquipmentDefect", "MiExternalEmergencyStop", "MiExternalPlcGeneral", "MiHydraulicFailure", "MiTankOutOfBounds", "MiHatchOpen", "MiFunctionStateMismatch", "MiPressureOutOfBounds", "MiPumpGeneral", "MiGasDetection", "MiWaterInFuel", "MiRedGroupActive", "MiNoFlowCoolingWater", "MiExternalSystemGeneral", "DaremiWindSpeedTooHigh", "DaremiWeightTooHigh", "DaremiTiltTooHigh"]
allowed_system_ids = ["BridgeWarningIndicator", "BridgeAlarmIndicator", "AlarmGeneral", "AlarmFire", "AcknowledgeAlarms", "AcknowledgeWarnings", "Reset", "CargoLights", "DeckLight1", "DeckLight2", "DeckLight3", "DeckLight4", "StrobeLights", "Siren", "UpMastPole1", "UpMastPole2", "DropAnchorStern1", "DropAnchorBow1", "WiFiAccessPoint", "SearchLight1", "UpMastPole3", "UpMastPole4", "DownSpudPoleStern1", "DownSpudPoleBow1", "CallSystemRoom1", "CallSystemRoom2", "CallSystemRoom3", "CallSystemRoom4", "LightMasthead", "LightSides", "LightStern", "LightTowing", "LightAnchor", "LightSpecial", "LightBlueSign", "LightHorn", "LightSail", "LightAdnrSign1", "LightAdnrSign2", "Horn"]
allowed_joystick_parsing_ids = ['X', 'Y', 'Z']
allowed_extra_ids = ['SpeedLevel0', 'SpeedLevelForward1', 'SpeedLevelReverse1', 'SpeedLevel2', 'SpeedLevel3', 'SpeedLevel4']

config_df = pd.read_excel('input/20250124SystemFunctionsInterfaces.xlsx', 'Dignity - Werkina - System func')
vehicle_id = 69

config_df.fillna('', inplace=True)
config_df['GroupId'] = config_df.groupby(['Ip', 'DomainDriverPurpose', 'DomainDriverId']).cumcount()



config_df

config_df.head(n=10)

config_df['GroupId']

config_df[config_df['AlarmMessage'] == 'Fan in alarm']

def generate_register_meta_data_config(register_type, register_id, register_value_type, bit_index):
    if register_type == 'Holding Register' or register_type == 'Input Register':
        if register_value_type.lower() == 'bool':
            return {
                "RegisterMetaData": {
                    "RegisterId": int(register_id),
                    "RegisterType": register_type.replace(' ', ''),
                    "RegisterValueType": "Boolean"
                },
                "RegisterParsingData": {
                    "BitIndex": int(bit_index)
                }
            }
        elif register_value_type.lower() == 'int':
            return {
                'RegisterMetaData': {
                    'RegisterId': int(register_id),
                    'RegisterType': register_type.replace(' ', ''),
                    'RegisterValueType': 'Integer16'
                }
            }
        else:
            raise Exception(f"Unexpected value type received: {register_value_type} for register id: {register_id}")
            
    elif register_type == 'Coil':
        return {
            "RegisterMetaData": {
                "RegisterId": int(register_id),
                "RegisterType": "Coil",
                "RegisterValueType": "Boolean"
            }
        }
    else:
        raise Exception("Unexpected register type received: " + register_type)

excel_id_to_domain_driver_identification = {
    "Bow360Thruster1": {
        "UnitId": 1,
        "UnitLocation": "Bow",
        "UnitType": "ThrusterRotational"
    },
    "Bow360Thruster2": {
        "UnitId": 2,
        "UnitLocation": "Bow",
        "UnitType": "ThrusterRotational"
    },
    "SternThruster1": {
        "UnitId": 1,
        "UnitLocation": "Stern",
        "UnitType": "ThrusterRotational"
    },
    "SternThruster2": {
        "UnitId": 2,
        "UnitLocation": "Stern",
        "UnitType": "ThrusterRotational"
    },
    "SternEngine1": {
        "UnitId": 1,
        "UnitLocation": "Stern",
        "UnitType": "Engine"
    },
    "SternEngine2": {
        "UnitId": 2,
        "UnitLocation": "Stern",
        "UnitType": "Engine"
    },
    "BowPipeThruster1": {
        "UnitId": 1,
        "UnitLocation": "Bow",
        "UnitType": "ThrusterTransversal"
    },
    "SternRudder1": {
        "UnitId": 1,
        "UnitLocation": "Stern",
        "UnitType": "Rudder"
    },
    "UnspecifiedThrusterGeneric1": {
        "UnitId": 1,
        "UnitLocation": "Unspecified",
        "UnitType": "ThrusterGeneric"
    },
    "UnspecifiedThrusterGeneric2": {
        "UnitId": 2,
        "UnitLocation": "Unspecified",
        "UnitType": "ThrusterGeneric"
    },
    "UnspecifiedAutoPilot1": {
        "UnitId": 1,
        "UnitLocation": "Unspecified",
        "UnitType": "AutoPilot"
    }
}

def domain_driver_id_to_string(domain_driver_id: dict):
    return f'{domain_driver_id["UnitLocation"]}{domain_driver_id["UnitType"]}_{domain_driver_id["UnitId"]}'

def prepare_config(row):
    domain_driver_id_parts = row['DomainDriverId'].split('_')
    domain_driver_id = domain_driver_id_parts[0]
    joystick_parsing_id = ''
    if len(domain_driver_id_parts) > 1:
        joystick_parsing_id = domain_driver_id_parts[1] 
        
    if row['Ip'] not in configs_per_ip:
        configs_per_ip[row['Ip']] = {
            "DriverConfig": {
                "DriverIdentification": {
                    "name": row['ServerName'],
                    "technicalComponentId": "",
                    "driverId": str(uuid.uuid4())
                },
                "rebootIntervalOnFail_ms": 1000,
                "heartbeatInterval_ms": 1000,
                "SendingIntervalMs": 250,
                "ReadingIntervalMs": 250,
                "GeneralSystemDomainDrivers": [],
                "PropulsionDomainDrivers": [],
                "SystemFunctionsDomainDrivers": [],
                "AlarmDomainDrivers": []
            },
            "ConnectionHandlerConfig": {
                "protocol": "modbustcpclient",
                "connectionAddress": row['Ip'],
                "connectionAddressOption": 502
            },
            "Pipelines": [
                "Automation"
            ]
        }
    if row['Ip'] not in system_configs_per_id_per_ip:
        system_configs_per_id_per_ip[row['Ip']] = {}
    system_configs_per_id = system_configs_per_id_per_ip[row['Ip']]
    if row['Type'] == 'HeartbeatWrite':
        heartbeat_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])
        configs_per_ip[row['Ip']]['DriverConfig']['HeartbeatRegister'] = heartbeat_config
        
    elif row['Type'] == 'SystemFunction' or row['Type'] == 'SystemFunctions':
        if domain_driver_id != '' and row['DomainDriverPurpose'] != '':
            
            if row['DomainDriverPurpose'] not in allowed_system_function_action_ids:
                raise Exception(f'Received an unknown system function action id: {row["DomainDriverPurpose"]}')

            if domain_driver_id not in allowed_system_ids:
                raise Exception(f'Received an unknown system id: {row["DomainDriverId"]}')
                
                
            bool_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])
            bool_config['SystemFunctionsValuePurposes'] = row['DomainDriverPurpose']
            
            if row['DomainDriverPurpose'] == 'ToggleActionPulse':
                bool_config['SystemFunctionsValuePurposes'] = 'ToggleAction'
                system_functions_pulse_configs[domain_driver_id] = {
                    'PulseTimeMs': 1000,
                    'DisableStateMismatchCheck': False
                }

            if domain_driver_id in system_configs_per_id:
                print(domain_driver_id)
                print(row['GroupId'])
                print(system_configs_per_id[domain_driver_id])
                if row['GroupId'] < len(system_configs_per_id[domain_driver_id]):
                    system_configs_per_id[domain_driver_id][row['GroupId']]['DomainSpecificConfig']['TrackedRegisters'].append(bool_config)
                else:
                    system_configs_per_id[domain_driver_id].append({
                        "DomainDriverIdentification": {
                            "SystemFunctionId": domain_driver_id,
                            "Description": row['Description']
                        },
                        "DomainSpecificConfig": {
                            "TrackedRegisters": [
                                bool_config
                            ]
                        }
                        })
            else:
                system_configs_per_id[domain_driver_id] = [{
                    "DomainDriverIdentification": {
                        "SystemFunctionId": domain_driver_id,
                        "Description": row['Description']
                    },
                    "DomainSpecificConfig": {
                        "TrackedRegisters": [
                            bool_config
                        ]
                    }
                }]
            
            
    elif row['Type'] == 'RemoteControlSwitch':
        remote_control_switch_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])
        remote_control_switch_config['GeneralSystemValuePurpose'] = 'RunSwitchFeedback'
        configs_per_ip[row['Ip']]['DriverConfig']['GeneralSystemDomainDrivers'].append(remote_control_switch_config)
    elif row['Type'] == 'EmergencyStopFeedback':
        remote_control_switch_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])
        remote_control_switch_config['RegisterParsingData']['InvertBitValue'] = True
        remote_control_switch_config['GeneralSystemValuePurpose'] = 'EmergencyStopFeedback'
        configs_per_ip[row['Ip']]['DriverConfig']['GeneralSystemDomainDrivers'].append(remote_control_switch_config)
    elif row['Type'] == 'Reserved':
        pass
    elif row['Type'] == 'HeartbeatRead':
        heartbeat_read_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])
        heartbeat_read_config['GeneralSystemValuePurpose'] = 'ThirdPartyHeartbeat'
        configs_per_ip[row['Ip']]['DriverConfig']['GeneralSystemDomainDrivers'].append(heartbeat_read_config)
    elif row['Type'] == 'Propulsion':
        if row['Ip'] not in propulsion_configs_per_id_per_ip:
            propulsion_configs_per_id_per_ip[row['Ip']] = {}
            
        propulsion_configs_per_id = propulsion_configs_per_id_per_ip[row['Ip']]
        propulsion_config = {}
        if domain_driver_id in propulsion_configs_per_id:
            propulsion_config = propulsion_configs_per_id[domain_driver_id]
        else:
            if domain_driver_id not in excel_id_to_domain_driver_identification:
                raise Exception(f'Received a propulsion misconfig for {row}')
            propulsion_config = {
                'DomainDriverIdentification': excel_id_to_domain_driver_identification[domain_driver_id],
                'DomainSpecificConfig': {
                    'ClutchValues': {
                        "DetentForwardValue": 20,
                        "ClutchForwardValue": 20,
                        "DetentBackwardsValue": -20,
                        "ClutchBackwardsValue": -20
                    },
                    "TrackedRegisters": []
                }
            }
        if row['DomainDriverPurpose'] not in allowed_propulsion_action_ids:
            raise Exception(f'Received an unknown propulsion action: {row["DomainDriverPurpose"]}')
        tracked_register = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])
        
        if joystick_parsing_id != '':
            if joystick_parsing_id in allowed_joystick_parsing_ids:
                joystick_parsing_data = {
                    'JoystickValueProperty': joystick_parsing_id
                }
            else:
                raise Exception(f'Received an unexpected joystick parsing id: {joystick_parsing_id}')
            tracked_register['JoystickParsingData'] = joystick_parsing_data
        if 'ExtraId' in row:
            if row['ExtraId'] in allowed_extra_ids:
                tracked_register['ArconPropulsionValuePurposeMetaData'] = {
                                        "ArconPropulsionValuePurposeType": row['ExtraId']
                                    }
        propulsion_value_purpose = row['DomainDriverPurpose']
        if propulsion_value_purpose == 'ToggleActionPulse':
            propulsion_value_purpose = 'ActionTogglePropulsion'
            propulsion_id_str = domain_driver_id_to_string(propulsion_config['DomainDriverIdentification'])
            propulsion_pulse_configs[propulsion_id_str] = {
                'PulseTimeMs': 3000,
                'DisableStateMismatchCheck': False
            }
        elif propulsion_value_purpose == 'ActionTogglePropulsion':
            #ensure that default we use a pulse
            propulsion_id_str = domain_driver_id_to_string(propulsion_config['DomainDriverIdentification'])
            propulsion_pulse_configs[propulsion_id_str] = {
                'PulseTimeMs': 3000,
                'DisableStateMismatchCheck': False
            }
        tracked_register['PropulsionValuePurpose'] = propulsion_value_purpose
        if 'Thruster' in domain_driver_id and 'Direction' in propulsion_value_purpose:
            tracked_register['RegisterScaleData'] = {
                'ScalingType': 'ThrusterPercentageToMovementDegrees'
            }
        propulsion_config['DomainSpecificConfig']['TrackedRegisters'].append(tracked_register)
        propulsion_configs_per_id[domain_driver_id] = propulsion_config
        propulsion_configs_per_id_per_ip[row['Ip']] = propulsion_configs_per_id
        configs_per_ip[row['Ip']]['DriverConfig']['PropulsionDomainDrivers'] = list(propulsion_configs_per_id.values())
    elif row['Type'] == '':
        pass
    elif row['Type'] == 'Alarms':
        if domain_driver_id not in allowed_alarm_ids:
            raise Exception(f'Received an unknown alarm id: {row["DomainDriverId"]}')

        bool_config = generate_register_meta_data_config(row['RegisterType'], row['Register'], row['ValueType'], row['Bit'])
        
        alarm_domain_driver = {
            'DomainDriverIdentification': {
                'AlarmMessage': row['AlarmMessage'],
                'NotificationGroupId': row['NotificationGroupId'],
                'EntityId': row['EntityId'],
                'NotificationTypeId': row['NotificationTypeId']
            },
            'DomainSpecificConfig': bool_config
        }
        
        if alarm_domain_driver['DomainDriverIdentification']['NotificationTypeId'] == 'Warning':
            alarm_domain_driver['DomainDriverIdentification']['WarningId'] = domain_driver_id
        else:
            alarm_domain_driver['DomainDriverIdentification']['AlarmId'] = domain_driver_id
        for k,v in alarm_domain_driver['DomainDriverIdentification'].items():
            if k != 'EntityId' and v.strip() == '':
                raise Exception(f'Received a misconfig for {k}. {alarm_domain_driver}')
        configs_per_ip[row['Ip']]['DriverConfig']['AlarmDomainDrivers'].append(alarm_domain_driver)

    else:
        raise Exception(f'Received invalid type: {row["Type"]}')
        
        

safety_system_io_config = {
            "DriverConfig": {
                "DriverIdentification": {
                    "name": "Operator panel - Moxa",
                    "technicalComponentId": "",
                    "driverId": "626b9a03-f1f1-4cdc-a6f8-32785617bf3b"
                },
                "rebootIntervalOnFail_ms": 1000,
                "heartbeatInterval_ms": 1000,
                "SendingIntervalMs": 250,
                "ReadingIntervalMs": 250,
                "GeneralSystemDomainDrivers": [
                    {
                        "RegisterMetaData": {
                            "RegisterId": 32,
                            "RegisterType": "HoldingRegister",
                            "RegisterValueType": "Boolean"
                        },
                        "RegisterParsingData": {
                            "BitIndex": 0
                        },
                        "GeneralSystemValuePurpose": "SafetyBuzzerControl"
                    },
                    {
                        "RegisterMetaData": {
                            "RegisterId": 48,
                            "RegisterType": "InputRegister",
                            "RegisterValueType": "Boolean"
                        },
                        "RegisterParsingData": {
                            "BitIndex": 0
                        },
                        "GeneralSystemValuePurpose": "LocalControlRequest"
                    },
                    {
                        "RegisterMetaData": {
                            "RegisterId": 32,
                            "RegisterType": "HoldingRegister",
                            "RegisterValueType": "Boolean"
                        },
                        "RegisterParsingData": {
                            "BitIndex": 2
                        },
                        "GeneralSystemValuePurpose": "RunStateLedControl"
                    },
                    {
                        "RegisterMetaData": {
                            "RegisterId": 32,
                            "RegisterType": "HoldingRegister",
                            "RegisterValueType": "Boolean"
                        },
                        "RegisterParsingData": {
                            "BitIndex": 1
                        },
                        "GeneralSystemValuePurpose": "SafetyLed"
                    },
                    {
                        "RegisterMetaData": {
                            "RegisterId": 32,
                            "RegisterType": "HoldingRegister",
                            "RegisterValueType": "Boolean"
                        },
                        "RegisterParsingData": {
                            "BitIndex": 3
                        },
                        "GeneralSystemValuePurpose": "ResetSafetySystem"
                    }
                ],
                "PropulsionDomainDrivers": [],
                "SystemFunctionsDomainDrivers": [],
                "AlarmDomainDrivers": []
            },
            "ConnectionHandlerConfig": {
                "protocol": "modbustcpclient",
                "connectionAddress": "*************",
                "connectionAddressOption": 502
            },
            "Pipelines": [
                "Automation"
            ]
        }

cabinet_readout_io_moxa = {
    "DriverConfig": {
        "DriverIdentification": {
            "name": "Cabinet readout IO - Moxa",
            "technicalComponentId": "",
            "driverId": "dd5bd1e3-e4fc-4098-9b25-6aeab8fa6e89"
        },
        "rebootIntervalOnFail_ms": 1000,
        "heartbeatInterval_ms": 1000,
        "SendingIntervalMs": 250,
        "ReadingIntervalMs": 250,
        "GeneralSystemDomainDrivers": [],
        "PropulsionDomainDrivers": [],
        "SystemFunctionsDomainDrivers": [],
        "AlarmDomainDrivers": [
            {
                "DomainDriverIdentification": {
                    "AlarmMessage": "Stabilizer channel 1 not ok",
                    "NotificationGroupId": "PowerGeneral",
                    "EntityId": "Stabilizer 1",
                    "NotificationTypeId": "Warning",
                    "WarningId": "MiPowerGeneral"
                },
                "DomainSpecificConfig": {
                    "RegisterMetaData": {
                        "RegisterId": 48,
                        "RegisterType": "InputRegister",
                        "RegisterValueType": "Boolean"
                    },
                    "RegisterParsingData": {
                        "BitIndex": 0,
                        "InvertBitValue": True
                    }
                }
            },
            {
                "DomainDriverIdentification": {
                    "AlarmMessage": "Stabilizer channel 2 not ok",
                    "NotificationGroupId": "PowerGeneral",
                    "EntityId": "Stabilizer 2",
                    "NotificationTypeId": "Warning",
                    "WarningId": "MiPowerGeneral"
                },
                "DomainSpecificConfig": {
                    "RegisterMetaData": {
                        "RegisterId": 48,
                        "RegisterType": "InputRegister",
                        "RegisterValueType": "Boolean"
                    },
                    "RegisterParsingData": {
                        "BitIndex": 1,
                        "InvertBitValue": True
                    }
                }
            },
            {
                "DomainDriverIdentification": {
                    "AlarmMessage": "12V Distribution not ok",
                    "NotificationGroupId": "PowerGeneral",
                    "EntityId": "12V Core cabinet",
                    "NotificationTypeId": "Warning",
                    "WarningId": "MiPowerGeneral"
                },
                "DomainSpecificConfig": {
                    "RegisterMetaData": {
                        "RegisterId": 48,
                        "RegisterType": "InputRegister",
                        "RegisterValueType": "Boolean"
                    },
                    "RegisterParsingData": {
                        "BitIndex": 3,
                        "InvertBitValue": True
                    }
                }
            },
            {
                "DomainDriverIdentification": {
                    "AlarmMessage": "Cooling 5 degrees above set temperature of 35 degrees",
                    "NotificationGroupId": "ControlSystemGeneral",
                    "EntityId": "Cooling Core cabinet",
                    "NotificationTypeId": "Warning",
                    "WarningId": "MiTemperatureExceededThresholds"
                },
                "DomainSpecificConfig": {
                    "RegisterMetaData": {
                        "RegisterId": 48,
                        "RegisterType": "InputRegister",
                        "RegisterValueType": "Boolean"
                    },
                    "RegisterParsingData": {
                        "BitIndex": 4,
                        "InvertBitValue": True
                    }
                }
            },
            {
                "DomainDriverIdentification": {
                    "AlarmMessage": "Core cabinets door opened",
                    "NotificationGroupId": "ControlSystemGeneral",
                    "EntityId": "Door Core cabinet",
                    "NotificationTypeId": "Warning",
                    "WarningId": "MiHatchOpen"
                },
                "DomainSpecificConfig": {
                    "RegisterMetaData": {
                        "RegisterId": 48,
                        "RegisterType": "InputRegister",
                        "RegisterValueType": "Boolean"
                    },
                    "RegisterParsingData": {
                        "BitIndex": 5,
                        "InvertBitValue": True
                    }
                }
            }
        ]
    },
    "ConnectionHandlerConfig": {
        "protocol": "modbustcpclient",
        "connectionAddress": "*************",
        "connectionAddressOption": 502
    },
    "Pipelines": [
        "Automation"
    ]
}

safety_system_pilz_config = {
            "DriverConfig": {
                "DriverIdentification": {
                    "name": "Safety system - Pilz",
                    "technicalComponentId": "",
                    "driverId": "e46936ab-d2e4-42eb-9333-85b0e4f133e4"
                },
                "rebootIntervalOnFail_ms": 1000,
                "heartbeatInterval_ms": 1000,
                "SendingIntervalMs": 500,
                "ReadingIntervalMs": 250,
                "GeneralSystemDomainDrivers": [
                    {
                        "RegisterMetaData": {
                            "RegisterId": 1060,
                            "RegisterType": "HoldingRegister",
                            "RegisterValueType": "Boolean"
                        },
                        "RegisterParsingData": {
                            "BitIndex": 2
                        },
                        "GeneralSystemValuePurpose": "RunSwitchFeedback"
                    },
                    {
                        "RegisterMetaData": {
                            "RegisterId": 1060,
                            "RegisterType": "HoldingRegister",
                            "RegisterValueType": "Boolean"
                        },
                        "RegisterParsingData": {
                            "BitIndex": 0,
                            "InvertBitValue": True
                        },
                        "GeneralSystemValuePurpose": "EmergencyStopFeedback"
                    },
                    {
                        "RegisterMetaData": {
                            "RegisterId": 1062,
                            "RegisterType": "HoldingRegister",
                            "RegisterValueType": "Boolean"
                        },
                        "RegisterParsingData": {
                            "BitIndex": 2
                        },
                        "GeneralSystemValuePurpose": "SafetyOutputFeedback"
                     }
                ],
                "PropulsionDomainDrivers": [],
                "SystemFunctionsDomainDrivers": [],
                "AlarmDomainDrivers": []
            },
            "ConnectionHandlerConfig": {
                "protocol": "modbustcpclient",
                "connectionAddress": "*************",
                "connectionAddressOption": 502
            },
            "Pipelines": [
                "Automation"
            ]
        }

modbus_driver_configs = []
configs_per_ip = {
    f'10.0.{vehicle_id}.13': safety_system_io_config,
    f'10.1.{vehicle_id}.164': safety_system_pilz_config,
    f'10.0.{vehicle_id}.15': cabinet_readout_io_moxa
}

propulsion_pulse_configs = {}
system_functions_pulse_configs = {}


system_configs_per_id_per_ip = {}
propulsion_configs_per_id_per_ip = {}
_ = config_df.apply(prepare_config, axis=1)

for ip, system_configs_per_id in system_configs_per_id_per_ip.items():
    if ip != '':
        for _id, system_funcion_config  in system_configs_per_id.items():
            for config in system_funcion_config:
                configs_per_ip[ip]['DriverConfig']['SystemFunctionsDomainDrivers'].append(config)

amp_config = {'ModbusAutomationDriver': []}
for ip, config in configs_per_ip.items():
    if ip.strip() == '':
        continue
    config['ConnectionHandlerConfig'] =  {
        "protocol": "modbustcpclient",
        "connectionAddress": ip,
        "connectionAddressOption": 502
    }
    config['Pipelines'] = [
        'Automation'
    ]
    amp_config['ModbusAutomationDriver'].append(config)

amp_config

with open('amp_config.json', 'w') as f:
    json.dump(amp_config, f, indent=4)
with open('PropulsionSystemStateWatcherConfigs.json', 'w') as f:
    json.dump(propulsion_pulse_configs, f, indent=4)

with open('SystemFunctionsSystemStateWatcherConfigs.json', 'w') as f:
    json.dump(system_functions_pulse_configs, f, indent=4)









